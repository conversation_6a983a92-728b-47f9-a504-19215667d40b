"""
OAuth2密码流实现服务
提供标准OAuth2密码流认证功能
"""
from typing import Any, Dict, Optional
from fastapi import HTTPException, status, Depends
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm
from backend.app.modules.auth.models import User
from backend.app.shared.security import verify_password, create_access_token, create_refresh_token
from backend.app.shared.config import settings
from backend.app.shared.errors import ErrorCode

# OAuth2密码流配置
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=settings.oauth2_token_url)

async def authenticate_user_oauth2(username: str, password: str) -> Optional[Dict[str, Any]]:
    """
    OAuth2用户认证
    
    Args:
        username: 用户名（手机号或邮箱）
        password: 密码
        
    Returns:
        Dict[str, Any]: 用户信息字典，如果认证失败返回None
    """
    # 尝试通过手机号查找用户
    user = await User.filter(phone=username).first()
    
    # 如果手机号未找到，尝试通过邮箱查找
    if not user:
        user = await User.filter(email=username).first()
    
    # 用户不存在或密码错误
    if not user or not verify_password(password, user.password_hash):
        return None
    
    # 用户被禁用
    if not user.is_active:
        return None
    
    return {
        "id": user.id,
        "phone": user.phone,
        "email": user.email,
        "user_name": user.user_name,
        "role": user.role,
        "is_active": user.is_active,
    }

async def create_oauth2_tokens(user_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    创建OAuth2令牌响应
    
    Args:
        user_data: 用户数据字典
        
    Returns:
        Dict[str, Any]: OAuth2标准令牌响应
    """
    access_token = create_access_token(user_data["id"])
    refresh_token = create_refresh_token(user_data["id"])
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "expires_in": settings.access_expire_seconds,
        "user": user_data
    }

async def oauth2_login(form_data: OAuth2PasswordRequestForm = Depends()) -> Dict[str, Any]:
    """
    OAuth2密码流登录端点处理函数
    
    Args:
        form_data: OAuth2密码表单数据
        
    Returns:
        Dict[str, Any]: OAuth2令牌响应
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    user_data = await authenticate_user_oauth2(form_data.username, form_data.password)
    
    if not user_data:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return await create_oauth2_tokens(user_data)

async def get_current_user_oauth2(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """
    从OAuth2 Bearer Token获取当前用户
    
    Args:
        token: Bearer Token
        
    Returns:
        Dict[str, Any]: 当前用户信息
        
    Raises:
        HTTPException: Token无效时抛出401错误
    """
    from backend.app.shared.security import verify_token
    
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = verify_token(token, "access")
        user_id = int(payload.get("sub"))
        if user_id is None:
            raise credentials_exception
    except ValueError:
        raise credentials_exception
    
    # 获取用户信息
    user = await User.filter(id=user_id).first()
    if user is None:
        raise credentials_exception
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    
    return {
        "id": user.id,
        "phone": user.phone,
        "email": user.email,
        "user_name": user.user_name,
        "role": user.role,
        "is_active": user.is_active,
    }
