'use client'

import React, { useState } from 'react'
import { OAuth2AuthProvider } from '@/lib/auth/oauth2-context'
import { OAuth2LoginForm } from '@/components/auth/oauth2-login-form'
import { AuthStatus } from '@/components/auth/auth-status'

function OAuth2TestContent() {
  const [message, setMessage] = useState<string>('')
  const [messageType, setMessageType] = useState<'success' | 'error'>('success')

  const handleLoginSuccess = () => {
    setMessage('OAuth2登录成功！')
    setMessageType('success')
    setTimeout(() => setMessage(''), 3000)
  }

  const handleLoginError = (error: string) => {
    setMessage(error)
    setMessageType('error')
    setTimeout(() => setMessage(''), 5000)
  }

  return (
    <div className="min-h-screen bg-gray-100 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            OAuth2 认证测试页面
          </h1>
          <p className="text-gray-600">
            测试OAuth2密码流和Bearer Token认证功能
          </p>
        </div>

        {/* 消息提示 */}
        {message && (
          <div className={`mb-6 p-4 rounded-md ${
            messageType === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-700' 
              : 'bg-red-50 border border-red-200 text-red-700'
          }`}>
            {message}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：登录表单 */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              OAuth2 登录
            </h2>
            <OAuth2LoginForm 
              onSuccess={handleLoginSuccess}
              onError={handleLoginError}
            />
            
            {/* 测试说明 */}
            <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-blue-800 mb-2">测试说明</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• 使用手机号: 13800138000</li>
                <li>• 密码: password123</li>
                <li>• 支持手机号或邮箱登录</li>
                <li>• 成功后会获得Bearer Token</li>
              </ul>
            </div>
          </div>

          {/* 右侧：认证状态 */}
          <div>
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              认证状态
            </h2>
            <AuthStatus />
            
            {/* 功能特性 */}
            <div className="bg-white shadow-md rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                OAuth2 功能特性
              </h3>
              <div className="space-y-3">
                <div className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <div>
                    <div className="font-medium text-gray-800">OAuth2密码流</div>
                    <div className="text-sm text-gray-600">标准OAuth2密码流认证</div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <div>
                    <div className="font-medium text-gray-800">Bearer Token</div>
                    <div className="text-sm text-gray-600">JWT Bearer Token认证</div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <div>
                    <div className="font-medium text-gray-800">向后兼容</div>
                    <div className="text-sm text-gray-600">同时支持Cookie认证</div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <div>
                    <div className="font-medium text-gray-800">自动刷新</div>
                    <div className="text-sm text-gray-600">Token过期自动处理</div>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <span className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  <div>
                    <div className="font-medium text-gray-800">安全存储</div>
                    <div className="text-sm text-gray-600">LocalStorage安全存储</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* API端点信息 */}
        <div className="mt-8 bg-white shadow-md rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            新增的OAuth2 API端点
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-50 rounded-md p-4">
              <div className="font-medium text-gray-800 mb-2">
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm font-mono">POST</span>
                <span className="ml-2">/auth/token</span>
              </div>
              <div className="text-sm text-gray-600">
                OAuth2密码流获取访问令牌
              </div>
            </div>
            
            <div className="bg-gray-50 rounded-md p-4">
              <div className="font-medium text-gray-800 mb-2">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-mono">GET</span>
                <span className="ml-2">/auth/me</span>
              </div>
              <div className="text-sm text-gray-600">
                获取当前用户信息（支持Bearer Token和Cookie）
              </div>
            </div>
            
            <div className="bg-gray-50 rounded-md p-4">
              <div className="font-medium text-gray-800 mb-2">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm font-mono">GET</span>
                <span className="ml-2">/auth/me/bearer</span>
              </div>
              <div className="text-sm text-gray-600">
                获取当前用户信息（仅支持Bearer Token）
              </div>
            </div>
            
            <div className="bg-gray-50 rounded-md p-4">
              <div className="font-medium text-gray-800 mb-2">
                <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm font-mono">ALL</span>
                <span className="ml-2">现有端点</span>
              </div>
              <div className="text-sm text-gray-600">
                所有现有端点保持向后兼容
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function OAuth2TestPage() {
  return (
    <OAuth2AuthProvider>
      <OAuth2TestContent />
    </OAuth2AuthProvider>
  )
}
