"""
认证依赖模块
提供各种认证和授权依赖函数
"""
from typing import Dict, Any, List
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import HTTPAuthorizationCredentials
from backend.app.shared.security import (
    bearer_scheme, 
    get_current_user_bearer, 
    get_current_user_flexible
)
from backend.app.modules.auth.models import UserRole

async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    获取当前用户（支持Bearer Token和Cookie认证）
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        Dict[str, Any]: 当前用户信息
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    return await get_current_user_flexible(request)

async def get_current_user_bearer_only(
    credentials: HTTPAuthorizationCredentials = Depends(bearer_scheme)
) -> Dict[str, Any]:
    """
    仅通过Bearer Token获取当前用户
    
    Args:
        credentials: HTTP Authorization credentials
        
    Returns:
        Dict[str, Any]: 当前用户信息
        
    Raises:
        HTTPException: 认证失败时抛出401错误
    """
    return await get_current_user_bearer(credentials)

async def get_current_active_user(
    current_user: Dict[str, Any] = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    获取当前活跃用户
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        Dict[str, Any]: 当前活跃用户信息
        
    Raises:
        HTTPException: 用户未激活时抛出400错误
    """
    if not current_user.get("is_active"):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user

def require_roles(*allowed_roles: UserRole):
    """
    角色权限检查依赖工厂函数
    
    Args:
        *allowed_roles: 允许的用户角色列表
        
    Returns:
        function: 依赖函数
    """
    async def role_checker(
        current_user: Dict[str, Any] = Depends(get_current_active_user)
    ) -> Dict[str, Any]:
        user_role = current_user.get("role")
        
        # 如果用户角色是枚举类型，获取其值
        if hasattr(user_role, "value"):
            user_role = user_role.value
        
        # 检查用户角色是否在允许的角色列表中
        allowed_role_values = [role.value if hasattr(role, "value") else str(role) for role in allowed_roles]
        
        if user_role not in allowed_role_values:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
        
        return current_user
    
    return role_checker

async def require_admin(
    current_user: Dict[str, Any] = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    管理员权限检查依赖
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        Dict[str, Any]: 当前管理员用户信息
        
    Raises:
        HTTPException: 权限不足时抛出403错误
    """
    user_role = current_user.get("role")
    
    # 如果用户角色是枚举类型，获取其值
    if hasattr(user_role, "value"):
        user_role = user_role.value
    
    if user_role != UserRole.ADMIN.value:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    
    return current_user

async def require_user(
    current_user: Dict[str, Any] = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    普通用户权限检查依赖（包括管理员）
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        Dict[str, Any]: 当前用户信息
    """
    # 任何活跃用户都可以访问
    return current_user

# 便捷的角色检查依赖
require_admin_role = require_roles(UserRole.ADMIN)
require_user_role = require_roles(UserRole.USER, UserRole.ADMIN)
