from typing import Any, Dict
from passlib.context import CryptContext
from backend.app.modules.auth.models import User, UserRole
import re
from backend.app.shared.security import hash_password, verify_password, create_access_token, create_refresh_token, verify_token
from fastapi import Request
from backend.app.shared.security import COOKIE_ACCESS
from backend.app.shared.errors import ErrorCode

# # 使用bcrypt算法的密码上下文
# _pwd_ctx = CryptContext(schemes=["bcrypt"], deprecated="auto")

# 手机号校验
_PHONE_RE = re.compile(r"^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$")

def _validate_phone(phone: str) -> None:
    if not phone or not _PHONE_RE.match(phone):
        raise ValueError(ErrorCode.INVALID_PHONE)
    
def _validate_password(password: str) -> None:
    if not password or len(password) < 8 or len(password) > 64:
        raise ValueError(ErrorCode.INVALID_PASSWORD)
    
async def register_with_phone(phone: str, password: str) -> Dict[str, Any]:
    """
    手机号注册核心逻辑
    
    Args:
        param phone: 手机号
        param password: 密码
    
    Returns:
        Dict[str, Any]: 返回用户信息
    """
    
    _validate_phone(phone)
    _validate_password(password)
    
    # 唯一性校验，手机号唯一
    exists = await User.filter(phone=phone).exists()
    if exists:
        raise ValueError(ErrorCode.PHONE_EXISTS)
    
    pwd_hash = hash_password(password)
    
    user = await User.create(
        user_name=phone,
        phone=phone,
        email=None,
        password_hash=pwd_hash,
        role=UserRole.USER,
        is_active=True
        )
    
    # 注册成功后生成JWT令牌
    access_token = create_access_token(user.id)
    refresh_token = create_refresh_token(user.id)
    
    return {
        "id": user.id,
        "phone": user.phone,
        "user_name": user.user_name,
        "role": user.role,
        "is_active": user.is_active,
        "access_token": access_token,
        "refresh_token": refresh_token
    }
    
async def login_with_phone(phone: str, password: str) -> Dict[str, Any]:
    """
    手机号登录核心逻辑
    Args:
        param phone: 手机号
        param password: 密码
    Returns:
        Dict[str, Any]: 返回用户信息
    """
    
    _validate_phone(phone)
    _validate_password(password)
    
    # 查询用户
    user = await User.filter(phone=phone).first()
    if not user:
        raise ValueError(ErrorCode.INVALID_CREDENTIALS)
    if not verify_password(password, user.password_hash):
        raise ValueError(ErrorCode.INVALID_CREDENTIALS)
    
    # 生成JWT令牌
    access_token = create_access_token(user.id)
    refresh_token = create_refresh_token(user.id)
    
    return {
        "id": user.id,
        "phone": user.phone,
        "user_name": user.user_name,
        "role": user.role,
        "is_active": user.is_active,
        "access_token": access_token,
        "refresh_token": refresh_token
    }

async def get_current_user(request: Request) -> Dict[str, Any]:
    """
    获取当前用户信息
    
    Args:
        param request: FastAPI请求对象
    
    Returns:
        Dict[str, Any]: 返回用户信息
    """
    # 从Cookie中获取access_token
    access_token = request.cookies.get(COOKIE_ACCESS)
    if not access_token:
        raise ValueError(ErrorCode.UNAUTHORIZED)
    
    # 验证令牌
    try:
        payload = verify_token(access_token, "access")
        user_id = int(payload.get("sub"))  # 转换为整数
    except ValueError:
        raise ValueError(ErrorCode.INVALID_TOKEN)
    
    # 查询用户
    user = await User.filter(id=user_id).first()
    if not user:
        raise ValueError(ErrorCode.USER_NOT_FOUND)
    
    return {
        "id": user.id,
        "phone": user.phone,
        "user_name": user.user_name,
        "role": user.role,
        "is_active": user.is_active
    }

