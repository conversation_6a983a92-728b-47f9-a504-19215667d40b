'use client'

import React from 'react'
import { useOAuth2Auth } from '@/lib/auth/oauth2-context'

export function AuthStatus() {
  const { 
    user, 
    isAuthenticated, 
    authMode, 
    getAccessToken, 
    hasValidToken,
    logout 
  } = useOAuth2Auth()

  const accessToken = getAccessToken()
  const tokenValid = hasValidToken()

  const getAuthModeDisplay = () => {
    switch (authMode) {
      case 'bearer':
        return { text: 'Bearer Token', color: 'bg-green-100 text-green-800' }
      case 'cookie':
        return { text: 'Cookie', color: 'bg-blue-100 text-blue-800' }
      case 'hybrid':
        return { text: 'Hybrid', color: 'bg-purple-100 text-purple-800' }
      default:
        return { text: 'Unknown', color: 'bg-gray-100 text-gray-800' }
    }
  }

  const authModeDisplay = getAuthModeDisplay()

  if (!isAuthenticated) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 mb-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">认证状态</h3>
        <div className="space-y-2">
          <div className="flex items-center">
            <span className="w-3 h-3 bg-red-500 rounded-full mr-2"></span>
            <span className="text-gray-700">未认证</span>
          </div>
          <div className="text-sm text-gray-500">
            请使用传统登录或OAuth2登录进行身份验证
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white shadow-md rounded-lg p-6 mb-4">
      <div className="flex justify-between items-start mb-4">
        <h3 className="text-lg font-semibold text-gray-800">认证状态</h3>
        <button
          onClick={logout}
          className="text-sm bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded transition duration-200"
        >
          退出登录
        </button>
      </div>

      <div className="space-y-3">
        {/* 认证状态 */}
        <div className="flex items-center">
          <span className="w-3 h-3 bg-green-500 rounded-full mr-2"></span>
          <span className="text-gray-700 font-medium">已认证</span>
        </div>

        {/* 认证模式 */}
        <div className="flex items-center">
          <span className="text-sm text-gray-600 mr-2">认证模式:</span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${authModeDisplay.color}`}>
            {authModeDisplay.text}
          </span>
        </div>

        {/* 用户信息 */}
        <div className="bg-gray-50 rounded-md p-3">
          <h4 className="text-sm font-medium text-gray-800 mb-2">用户信息</h4>
          <div className="space-y-1 text-sm">
            <div>
              <span className="text-gray-600">ID:</span>
              <span className="ml-2 text-gray-800">{user?.id}</span>
            </div>
            <div>
              <span className="text-gray-600">手机号:</span>
              <span className="ml-2 text-gray-800">{user?.phone}</span>
            </div>
            <div>
              <span className="text-gray-600">用户名:</span>
              <span className="ml-2 text-gray-800">{user?.user_name}</span>
            </div>
            <div>
              <span className="text-gray-600">角色:</span>
              <span className="ml-2 text-gray-800">{user?.role}</span>
            </div>
            <div>
              <span className="text-gray-600">状态:</span>
              <span className={`ml-2 ${user?.is_active ? 'text-green-600' : 'text-red-600'}`}>
                {user?.is_active ? '活跃' : '禁用'}
              </span>
            </div>
          </div>
        </div>

        {/* Bearer Token信息 */}
        {authMode === 'bearer' && (
          <div className="bg-green-50 rounded-md p-3">
            <h4 className="text-sm font-medium text-gray-800 mb-2">Bearer Token信息</h4>
            <div className="space-y-1 text-sm">
              <div>
                <span className="text-gray-600">Token状态:</span>
                <span className={`ml-2 ${tokenValid ? 'text-green-600' : 'text-red-600'}`}>
                  {tokenValid ? '有效' : '已过期'}
                </span>
              </div>
              <div>
                <span className="text-gray-600">Token (前20字符):</span>
                <span className="ml-2 text-gray-800 font-mono text-xs">
                  {accessToken ? `${accessToken.substring(0, 20)}...` : 'N/A'}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Cookie认证信息 */}
        {authMode === 'cookie' && (
          <div className="bg-blue-50 rounded-md p-3">
            <h4 className="text-sm font-medium text-gray-800 mb-2">Cookie认证信息</h4>
            <div className="text-sm text-gray-600">
              使用HttpOnly Cookie进行身份验证
            </div>
          </div>
        )}

        {/* 混合模式信息 */}
        {authMode === 'hybrid' && (
          <div className="bg-purple-50 rounded-md p-3">
            <h4 className="text-sm font-medium text-gray-800 mb-2">混合认证模式</h4>
            <div className="text-sm text-gray-600">
              支持Bearer Token和Cookie双重认证方式
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
